t 5.31389 /NodeList/3/DeviceList/0/$ns3::AquaSimNetDevice/Phy/Tx ns3::AquaSimPacketStamp (PacketStamp: Pt(20) Pr(0) TxRange(500) Freq(25) Noise(0) PacketStatus(INVALID)
) ns3::AquaSimHeader (Packet header is  TxTime=+5.52e+07ns Size=69 Direction=DOWN NumForwards=1 Error=False UniqueID=0 Timestamp=+5.314e+09ns SenderAddr=04 DestAddr=05 NextHop=0255
) ns3::MacHeader (Mac Header is: SA=04 DA=0255 DemuxPType=OTHER
) ns3::AlohaHeader (Aloha Header: SendAddress=04, DestAddress=05, PacketType=DATA
) ns3::AquaSimHeader (Packet header is  TxTime=+0ns Size=64 Direction=DOWN NumForwards=0 Error=False UniqueID=0 Timestamp=+0ns SenderAddr=04 DestAddr=05 NextHop=0255
) Payload (size=64)
r 5.53976 /NodeList/4/DeviceList/0/$ns3::AquaSimNetDevice/Phy/Rx ns3::AquaSimHeader (Packet header is  TxTime=+1e+07ns Size=69 Direction=UP NumForwards=1 Error=False UniqueID=0 Timestamp=+5.314e+09ns SenderAddr=04 DestAddr=05 NextHop=0255
) ns3::MacHeader (Mac Header is: SA=04 DA=0255 DemuxPType=OTHER
) ns3::AlohaHeader (Aloha Header: SendAddress=04, DestAddress=05, PacketType=DATA
) ns3::AquaSimHeader (Packet header is  TxTime=+0ns Size=64 Direction=DOWN NumForwards=0 Error=False UniqueID=0 Timestamp=+0ns SenderAddr=04 DestAddr=05 NextHop=0255
) Payload (size=64)
t 6.19235 /NodeList/3/DeviceList/0/$ns3::AquaSimNetDevice/Phy/Tx ns3::AquaSimPacketStamp (PacketStamp: Pt(20) Pr(0) TxRange(500) Freq(25) Noise(0) PacketStatus(INVALID)
) ns3::AquaSimHeader (Packet header is  TxTime=+5.52e+07ns Size=69 Direction=DOWN NumForwards=1 Error=False UniqueID=1 Timestamp=+6.192e+09ns SenderAddr=04 DestAddr=03 NextHop=0255
) ns3::MacHeader (Mac Header is: SA=04 DA=0255 DemuxPType=OTHER
) ns3::AlohaHeader (Aloha Header: SendAddress=04, DestAddress=03, PacketType=DATA
) ns3::AquaSimHeader (Packet header is  TxTime=+0ns Size=64 Direction=DOWN NumForwards=0 Error=False UniqueID=1 Timestamp=+0ns SenderAddr=04 DestAddr=03 NextHop=0255
) Payload (size=64)
r 6.41822 /NodeList/4/DeviceList/0/$ns3::AquaSimNetDevice/Phy/Rx ns3::AquaSimHeader (Packet header is  TxTime=+1e+07ns Size=69 Direction=UP NumForwards=1 Error=False UniqueID=1 Timestamp=+6.192e+09ns SenderAddr=04 DestAddr=03 NextHop=0255
) ns3::MacHeader (Mac Header is: SA=04 DA=0255 DemuxPType=OTHER
) ns3::AlohaHeader (Aloha Header: SendAddress=04, DestAddress=03, PacketType=DATA
) ns3::AquaSimHeader (Packet header is  TxTime=+0ns Size=64 Direction=DOWN NumForwards=0 Error=False UniqueID=1 Timestamp=+0ns SenderAddr=04 DestAddr=03 NextHop=0255
) Payload (size=64)
t 13.3417 /NodeList/3/DeviceList/0/$ns3::AquaSimNetDevice/Phy/Tx ns3::AquaSimPacketStamp (PacketStamp: Pt(20) Pr(0) TxRange(500) Freq(25) Noise(0) PacketStatus(INVALID)
) ns3::AquaSimHeader (Packet header is  TxTime=+5.52e+07ns Size=69 Direction=DOWN NumForwards=1 Error=False UniqueID=2 Timestamp=+1.3342e+10ns SenderAddr=04 DestAddr=02 NextHop=0255
) ns3::MacHeader (Mac Header is: SA=04 DA=0255 DemuxPType=OTHER
) ns3::AlohaHeader (Aloha Header: SendAddress=04, DestAddress=02, PacketType=DATA
) ns3::AquaSimHeader (Packet header is  TxTime=+0ns Size=64 Direction=DOWN NumForwards=0 Error=False UniqueID=2 Timestamp=+0ns SenderAddr=04 DestAddr=02 NextHop=0255
) Payload (size=64)
t 13.4069 /NodeList/3/DeviceList/0/$ns3::AquaSimNetDevice/Phy/Tx ns3::AquaSimPacketStamp (PacketStamp: Pt(20) Pr(0) TxRange(500) Freq(25) Noise(0) PacketStatus(INVALID)
) ns3::AquaSimHeader (Packet header is  TxTime=+5.52e+07ns Size=69 Direction=DOWN NumForwards=1 Error=False UniqueID=3 Timestamp=+1.3407e+10ns SenderAddr=04 DestAddr=05 NextHop=0255
) ns3::MacHeader (Mac Header is: SA=04 DA=0255 DemuxPType=OTHER
) ns3::AlohaHeader (Aloha Header: SendAddress=04, DestAddress=05, PacketType=DATA
) ns3::AquaSimHeader (Packet header is  TxTime=+0ns Size=64 Direction=DOWN NumForwards=0 Error=False UniqueID=3 Timestamp=+0ns SenderAddr=04 DestAddr=05 NextHop=0255
) Payload (size=64)
r 13.5676 /NodeList/4/DeviceList/0/$ns3::AquaSimNetDevice/Phy/Rx ns3::AquaSimHeader (Packet header is  TxTime=+1e+07ns Size=69 Direction=UP NumForwards=1 Error=False UniqueID=2 Timestamp=+1.3342e+10ns SenderAddr=04 DestAddr=02 NextHop=0255
) ns3::MacHeader (Mac Header is: SA=04 DA=0255 DemuxPType=OTHER
) ns3::AlohaHeader (Aloha Header: SendAddress=04, DestAddress=02, PacketType=DATA
) ns3::AquaSimHeader (Packet header is  TxTime=+0ns Size=64 Direction=DOWN NumForwards=0 Error=False UniqueID=2 Timestamp=+0ns SenderAddr=04 DestAddr=02 NextHop=0255
) Payload (size=64)
r 13.6328 /NodeList/4/DeviceList/0/$ns3::AquaSimNetDevice/Phy/Rx ns3::AquaSimHeader (Packet header is  TxTime=+1e+07ns Size=69 Direction=UP NumForwards=1 Error=False UniqueID=3 Timestamp=+1.3407e+10ns SenderAddr=04 DestAddr=05 NextHop=0255
) ns3::MacHeader (Mac Header is: SA=04 DA=0255 DemuxPType=OTHER
) ns3::AlohaHeader (Aloha Header: SendAddress=04, DestAddress=05, PacketType=DATA
) ns3::AquaSimHeader (Packet header is  TxTime=+0ns Size=64 Direction=DOWN NumForwards=0 Error=False UniqueID=3 Timestamp=+0ns SenderAddr=04 DestAddr=05 NextHop=0255
) Payload (size=64)
t 19.4411 /NodeList/3/DeviceList/0/$ns3::AquaSimNetDevice/Phy/Tx ns3::AquaSimPacketStamp (PacketStamp: Pt(20) Pr(0) TxRange(500) Freq(25) Noise(0) PacketStatus(INVALID)
) ns3::AquaSimHeader (Packet header is  TxTime=+5.52e+07ns Size=69 Direction=DOWN NumForwards=1 Error=False UniqueID=4 Timestamp=+1.9441e+10ns SenderAddr=04 DestAddr=01 NextHop=0255
) ns3::MacHeader (Mac Header is: SA=04 DA=0255 DemuxPType=OTHER
) ns3::AlohaHeader (Aloha Header: SendAddress=04, DestAddress=01, PacketType=DATA
) ns3::AquaSimHeader (Packet header is  TxTime=+0ns Size=64 Direction=DOWN NumForwards=0 Error=False UniqueID=4 Timestamp=+0ns SenderAddr=04 DestAddr=01 NextHop=0255
) Payload (size=64)
r 19.667 /NodeList/4/DeviceList/0/$ns3::AquaSimNetDevice/Phy/Rx ns3::AquaSimHeader (Packet header is  TxTime=+1e+07ns Size=69 Direction=UP NumForwards=1 Error=False UniqueID=4 Timestamp=+1.9441e+10ns SenderAddr=04 DestAddr=01 NextHop=0255
) ns3::MacHeader (Mac Header is: SA=04 DA=0255 DemuxPType=OTHER
) ns3::AlohaHeader (Aloha Header: SendAddress=04, DestAddress=01, PacketType=DATA
) ns3::AquaSimHeader (Packet header is  TxTime=+0ns Size=64 Direction=DOWN NumForwards=0 Error=False UniqueID=4 Timestamp=+0ns SenderAddr=04 DestAddr=01 NextHop=0255
) Payload (size=64)
t 19.6719 /NodeList/3/DeviceList/0/$ns3::AquaSimNetDevice/Phy/Tx ns3::AquaSimPacketStamp (PacketStamp: Pt(20) Pr(0) TxRange(500) Freq(25) Noise(0) PacketStatus(INVALID)
) ns3::AquaSimHeader (Packet header is  TxTime=+5.52e+07ns Size=69 Direction=DOWN NumForwards=1 Error=False UniqueID=5 Timestamp=+1.9672e+10ns SenderAddr=04 DestAddr=03 NextHop=0255
) ns3::MacHeader (Mac Header is: SA=04 DA=0255 DemuxPType=OTHER
) ns3::AlohaHeader (Aloha Header: SendAddress=04, DestAddress=03, PacketType=DATA
) ns3::AquaSimHeader (Packet header is  TxTime=+0ns Size=64 Direction=DOWN NumForwards=0 Error=False UniqueID=5 Timestamp=+0ns SenderAddr=04 DestAddr=03 NextHop=0255
) Payload (size=64)
r 19.8978 /NodeList/4/DeviceList/0/$ns3::AquaSimNetDevice/Phy/Rx ns3::AquaSimHeader (Packet header is  TxTime=+1e+07ns Size=69 Direction=UP NumForwards=1 Error=False UniqueID=5 Timestamp=+1.9672e+10ns SenderAddr=04 DestAddr=03 NextHop=0255
) ns3::MacHeader (Mac Header is: SA=04 DA=0255 DemuxPType=OTHER
) ns3::AlohaHeader (Aloha Header: SendAddress=04, DestAddress=03, PacketType=DATA
) ns3::AquaSimHeader (Packet header is  TxTime=+0ns Size=64 Direction=DOWN NumForwards=0 Error=False UniqueID=5 Timestamp=+0ns SenderAddr=04 DestAddr=03 NextHop=0255
) Payload (size=64)
t 23.2489 /NodeList/4/DeviceList/0/$ns3::AquaSimNetDevice/Phy/Tx ns3::AquaSimPacketStamp (PacketStamp: Pt(20) Pr(0) TxRange(500) Freq(25) Noise(0) PacketStatus(INVALID)
) ns3::AquaSimHeader (Packet header is  TxTime=+5.52e+07ns Size=69 Direction=DOWN NumForwards=1 Error=False UniqueID=6 Timestamp=+2.3249e+10ns SenderAddr=05 DestAddr=02 NextHop=0255
) ns3::MacHeader (Mac Header is: SA=05 DA=0255 DemuxPType=OTHER
) ns3::AlohaHeader (Aloha Header: SendAddress=05, DestAddress=02, PacketType=DATA
) ns3::AquaSimHeader (Packet header is  TxTime=+0ns Size=64 Direction=DOWN NumForwards=0 Error=False UniqueID=6 Timestamp=+0ns SenderAddr=05 DestAddr=02 NextHop=0255
) Payload (size=64)
r 23.4748 /NodeList/3/DeviceList/0/$ns3::AquaSimNetDevice/Phy/Rx ns3::AquaSimHeader (Packet header is  TxTime=+1e+07ns Size=69 Direction=UP NumForwards=1 Error=False UniqueID=6 Timestamp=+2.3249e+10ns SenderAddr=05 DestAddr=02 NextHop=0255
) ns3::MacHeader (Mac Header is: SA=05 DA=0255 DemuxPType=OTHER
) ns3::AlohaHeader (Aloha Header: SendAddress=05, DestAddress=02, PacketType=DATA
) ns3::AquaSimHeader (Packet header is  TxTime=+0ns Size=64 Direction=DOWN NumForwards=0 Error=False UniqueID=6 Timestamp=+0ns SenderAddr=05 DestAddr=02 NextHop=0255
) Payload (size=64)
t 31.411 /NodeList/3/DeviceList/0/$ns3::AquaSimNetDevice/Phy/Tx ns3::AquaSimPacketStamp (PacketStamp: Pt(20) Pr(0) TxRange(500) Freq(25) Noise(0) PacketStatus(INVALID)
) ns3::AquaSimHeader (Packet header is  TxTime=+5.52e+07ns Size=69 Direction=DOWN NumForwards=1 Error=False UniqueID=7 Timestamp=+3.1411e+10ns SenderAddr=04 DestAddr=03 NextHop=0255
) ns3::MacHeader (Mac Header is: SA=04 DA=0255 DemuxPType=OTHER
) ns3::AlohaHeader (Aloha Header: SendAddress=04, DestAddress=03, PacketType=DATA
) ns3::AquaSimHeader (Packet header is  TxTime=+0ns Size=64 Direction=DOWN NumForwards=0 Error=False UniqueID=7 Timestamp=+0ns SenderAddr=04 DestAddr=03 NextHop=0255
) Payload (size=64)
r 31.6369 /NodeList/4/DeviceList/0/$ns3::AquaSimNetDevice/Phy/Rx ns3::AquaSimHeader (Packet header is  TxTime=+1e+07ns Size=69 Direction=UP NumForwards=1 Error=False UniqueID=7 Timestamp=+3.1411e+10ns SenderAddr=04 DestAddr=03 NextHop=0255
) ns3::MacHeader (Mac Header is: SA=04 DA=0255 DemuxPType=OTHER
) ns3::AlohaHeader (Aloha Header: SendAddress=04, DestAddress=03, PacketType=DATA
) ns3::AquaSimHeader (Packet header is  TxTime=+0ns Size=64 Direction=DOWN NumForwards=0 Error=False UniqueID=7 Timestamp=+0ns SenderAddr=04 DestAddr=03 NextHop=0255
) Payload (size=64)
t 35.9805 /NodeList/4/DeviceList/0/$ns3::AquaSimNetDevice/Phy/Tx ns3::AquaSimPacketStamp (PacketStamp: Pt(20) Pr(0) TxRange(500) Freq(25) Noise(0) PacketStatus(INVALID)
) ns3::AquaSimHeader (Packet header is  TxTime=+5.52e+07ns Size=69 Direction=DOWN NumForwards=1 Error=False UniqueID=8 Timestamp=+3.598e+10ns SenderAddr=05 DestAddr=04 NextHop=0255
) ns3::MacHeader (Mac Header is: SA=05 DA=0255 DemuxPType=OTHER
) ns3::AlohaHeader (Aloha Header: SendAddress=05, DestAddress=04, PacketType=DATA
) ns3::AquaSimHeader (Packet header is  TxTime=+0ns Size=64 Direction=DOWN NumForwards=0 Error=False UniqueID=8 Timestamp=+0ns SenderAddr=05 DestAddr=04 NextHop=0255
) Payload (size=64)
r 36.2064 /NodeList/3/DeviceList/0/$ns3::AquaSimNetDevice/Phy/Rx ns3::AquaSimHeader (Packet header is  TxTime=+1e+07ns Size=69 Direction=UP NumForwards=1 Error=False UniqueID=8 Timestamp=+3.598e+10ns SenderAddr=05 DestAddr=04 NextHop=0255
) ns3::MacHeader (Mac Header is: SA=05 DA=0255 DemuxPType=OTHER
) ns3::AlohaHeader (Aloha Header: SendAddress=05, DestAddress=04, PacketType=DATA
) ns3::AquaSimHeader (Packet header is  TxTime=+0ns Size=64 Direction=DOWN NumForwards=0 Error=False UniqueID=8 Timestamp=+0ns SenderAddr=05 DestAddr=04 NextHop=0255
) Payload (size=64)
t 36.6119 /NodeList/1/DeviceList/0/$ns3::AquaSimNetDevice/Phy/Tx ns3::AquaSimPacketStamp (PacketStamp: Pt(20) Pr(0) TxRange(500) Freq(25) Noise(0) PacketStatus(INVALID)
) ns3::AquaSimHeader (Packet header is  TxTime=+5.52e+07ns Size=69 Direction=DOWN NumForwards=1 Error=False UniqueID=9 Timestamp=+3.6612e+10ns SenderAddr=02 DestAddr=03 NextHop=0255
) ns3::MacHeader (Mac Header is: SA=02 DA=0255 DemuxPType=OTHER
) ns3::AlohaHeader (Aloha Header: SendAddress=02, DestAddress=03, PacketType=DATA
) ns3::AquaSimHeader (Packet header is  TxTime=+0ns Size=64 Direction=DOWN NumForwards=0 Error=False UniqueID=9 Timestamp=+0ns SenderAddr=02 DestAddr=03 NextHop=0255
) Payload (size=64)
t 40.1749 /NodeList/2/DeviceList/0/$ns3::AquaSimNetDevice/Phy/Tx ns3::AquaSimPacketStamp (PacketStamp: Pt(20) Pr(0) TxRange(500) Freq(25) Noise(0) PacketStatus(INVALID)
) ns3::AquaSimHeader (Packet header is  TxTime=+5.52e+07ns Size=69 Direction=DOWN NumForwards=1 Error=False UniqueID=10 Timestamp=+4.0175e+10ns SenderAddr=03 DestAddr=01 NextHop=0255
) ns3::MacHeader (Mac Header is: SA=03 DA=0255 DemuxPType=OTHER
) ns3::AlohaHeader (Aloha Header: SendAddress=03, DestAddress=01, PacketType=DATA
) ns3::AquaSimHeader (Packet header is  TxTime=+0ns Size=64 Direction=DOWN NumForwards=0 Error=False UniqueID=10 Timestamp=+0ns SenderAddr=03 DestAddr=01 NextHop=0255
) Payload (size=64)
t 41.9243 /NodeList/4/DeviceList/0/$ns3::AquaSimNetDevice/Phy/Tx ns3::AquaSimPacketStamp (PacketStamp: Pt(20) Pr(0) TxRange(500) Freq(25) Noise(0) PacketStatus(INVALID)
) ns3::AquaSimHeader (Packet header is  TxTime=+5.52e+07ns Size=69 Direction=DOWN NumForwards=1 Error=False UniqueID=11 Timestamp=+4.1924e+10ns SenderAddr=05 DestAddr=03 NextHop=0255
) ns3::MacHeader (Mac Header is: SA=05 DA=0255 DemuxPType=OTHER
) ns3::AlohaHeader (Aloha Header: SendAddress=05, DestAddress=03, PacketType=DATA
) ns3::AquaSimHeader (Packet header is  TxTime=+0ns Size=64 Direction=DOWN NumForwards=0 Error=False UniqueID=11 Timestamp=+0ns SenderAddr=05 DestAddr=03 NextHop=0255
) Payload (size=64)
r 42.1502 /NodeList/3/DeviceList/0/$ns3::AquaSimNetDevice/Phy/Rx ns3::AquaSimHeader (Packet header is  TxTime=+1e+07ns Size=69 Direction=UP NumForwards=1 Error=False UniqueID=11 Timestamp=+4.1924e+10ns SenderAddr=05 DestAddr=03 NextHop=0255
) ns3::MacHeader (Mac Header is: SA=05 DA=0255 DemuxPType=OTHER
) ns3::AlohaHeader (Aloha Header: SendAddress=05, DestAddress=03, PacketType=DATA
) ns3::AquaSimHeader (Packet header is  TxTime=+0ns Size=64 Direction=DOWN NumForwards=0 Error=False UniqueID=11 Timestamp=+0ns SenderAddr=05 DestAddr=03 NextHop=0255
) Payload (size=64)
